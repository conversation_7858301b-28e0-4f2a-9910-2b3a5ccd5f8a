<template>
  <div class="hero-wrapper">
    <!-- 透明导航栏 -->
    <Navbar />

    <!-- 全屏轮播图区域 -->
    <section class="hero-carousel">
      <div class="carousel-container">
        <div class="carousel-slides" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
          <div v-for="(slide, index) in slides" :key="index" class="carousel-slide"
            :style="{ backgroundImage: `url(${slide.image})` }">
            <div class="slide-overlay"></div>
            <div class="slide-content">


            </div>
          </div>
        </div>

        <!-- 轮播指示器 -->
        <div class="carousel-indicators">
          <button v-for="(slide, index) in slides" :key="index" :class="{ active: currentSlide === index }"
            @click="goToSlide(index)" class="indicator"></button>
        </div>

        <!-- 轮播控制按钮 -->
        <button class="carousel-btn prev-btn" @click="prevSlide">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
        </button>
        <button class="carousel-btn next-btn" @click="nextSlide">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
        </button>
      </div>
    </section>
  </div>
</template>

<script>
import Navbar from '@/components/common/Navbar.vue'

export default {
  name: 'HeroSection',
  components: {
    Navbar
  },
  data() {
    return {
      currentSlide: 0,
      autoPlayTimer: null,
      slides: [
        {
          image: 'http://localhost:3000/images/轮播图/轮播1.png',
          title: '传承非遗文化 · 守护民族瑰宝',
          subtitle: '探索千年技艺，感受文化魅力，让非物质文化遗产在新时代绽放光彩'
        },
        {
          image: 'http://localhost:3000/images/轮播图/轮播2.png',
          title: '匠心传承 · 技艺永续',
          subtitle: '每一件作品都承载着深厚的历史底蕴和精湛的工艺技法'
        },
        {
          image: 'http://localhost:3000/images/轮播图/轮播3.png',
          title: '文化瑰宝 · 世代相传',
          subtitle: '在传统与现代的交融中，续写非遗文化的新篇章'
        },
        {
          image: 'http://localhost:3000/images/轮播图/轮播4.png',
          title: '非遗之美 · 匠人精神',
          subtitle: '用心守护每一份传统，让古老技艺在新时代重焕生机'
        }
      ]
    }
  },
  mounted() {
    this.startAutoPlay()
  },
  beforeUnmount() {
    this.stopAutoPlay()
  },
  methods: {
    nextSlide() {
      this.currentSlide = (this.currentSlide + 1) % this.slides.length
      this.resetAutoPlay()
    },
    prevSlide() {
      this.currentSlide = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1
      this.resetAutoPlay()
    },
    goToSlide(index) {
      this.currentSlide = index
      this.resetAutoPlay()
    },
    startAutoPlay() {
      this.autoPlayTimer = setInterval(() => {
        this.nextSlide()
      }, 5000) // 5秒自动切换
    },
    stopAutoPlay() {
      if (this.autoPlayTimer) {
        clearInterval(this.autoPlayTimer)
        this.autoPlayTimer = null
      }
    },
    resetAutoPlay() {
      this.stopAutoPlay()
      this.startAutoPlay()
    }
  }
}
</script>

<style scoped>
.hero-wrapper {
  position: relative;
}

/* 透明导航栏样式 */
.hero-wrapper :deep(.navbar) {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.hero-wrapper :deep(.nav-brand) {
  color: white !important;
  background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.hero-wrapper :deep(.nav-link) {
  color: rgba(255, 255, 255, 0.9) !important;
}

.hero-wrapper :deep(.nav-link:hover) {
  color: white !important;
  background: rgba(255, 255, 255, 0.2) !important;
}

.hero-wrapper :deep(.nav-link.router-link-active) {
  color: white !important;
  background: rgba(255, 255, 255, 0.3) !important;
}

.hero-wrapper :deep(.bar) {
  background: white !important;
}

/* 轮播图容器 */
.hero-carousel {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-slides {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel-slide {
  flex: 0 0 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(0, 0, 0, 0.4) 0%,
      rgba(0, 0, 0, 0.2) 50%,
      rgba(0, 0, 0, 0.6) 100%);
}

.slide-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 20px;
  animation: slideInUp 1s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.slide-subtitle {
  font-size: 1.5rem;
  margin-bottom: 40px;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  opacity: 0.95;
}

.slide-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.btn-primary,
.btn-secondary {
  padding: 18px 36px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  border: 2px solid rgba(255, 255, 255, 0.8);
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: white;
  transform: translateY(-3px);
}

/* 轮播指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: white;
  border-color: white;
  transform: scale(1.2);
}

.indicator:hover {
  border-color: white;
  background: rgba(255, 255, 255, 0.7);
}

/* 轮播控制按钮 */
.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.prev-btn {
  left: 30px;
}

.next-btn {
  right: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-carousel {
    height: 100vh;
  }

  .slide-title {
    font-size: 2.5rem;
  }

  .slide-subtitle {
    font-size: 1.2rem;
  }

  .slide-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary,
  .btn-secondary {
    padding: 15px 30px;
    font-size: 1rem;
  }

  .carousel-btn {
    width: 50px;
    height: 50px;
  }

  .prev-btn {
    left: 20px;
  }

  .next-btn {
    right: 20px;
  }

  .carousel-indicators {
    bottom: 20px;
  }
}

@media (max-width: 480px) {
  .slide-content {
    padding: 0 15px;
  }

  .slide-title {
    font-size: 2rem;
  }

  .slide-subtitle {
    font-size: 1rem;
  }
}
</style>