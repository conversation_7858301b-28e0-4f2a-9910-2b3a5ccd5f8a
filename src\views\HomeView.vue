<template>
  <div class="home-wrapper">
    <!-- 全屏滚动容器 -->
    <div class="fullpage-container" ref="container" @wheel="handleWheel" @touchstart="handleTouchStart"
      @touchmove="handleTouchMove">
      <div class="sections-wrapper" :style="{ transform: `translateY(-${currentSection * 100}vh)` }">
        <!-- 第一屏：英雄区域 -->
        <section class="fullpage-section hero-section" data-section="0">
          <HeroSection />
        </section>

        <!-- 第二屏：分类区域 -->
        <section class="fullpage-section" data-section="1">
          <div class="section-content">
            <CategoriesSection />
          </div>
        </section>

        <!-- 第三屏：精选区域 -->
        <section class="fullpage-section" data-section="2">
          <div class="section-content">
            <FeaturedSection />
          </div>
        </section>

        <!-- 第四屏：大师区域 -->
        <section class="fullpage-section" data-section="3">
          <div class="section-content">
            <MastersSection />
          </div>
        </section>
      </div>

      <!-- 右侧导航指示器 -->
      <div class="section-nav">
        <div v-for="(section, index) in sections" :key="index"
          :class="['nav-dot', { active: currentSection === index }]" @click="goToSection(index)" :title="section.title">
          <span class="nav-label">{{ section.title }}</span>
        </div>
      </div>

      <!-- 滚动提示 -->
      <div v-if="currentSection === 0" class="scroll-hint">
        <div class="scroll-arrow">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M7 13L12 18L17 13" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M7 6L12 11L17 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
        </div>
        <span class="scroll-text">向下滚动探索更多</span>
      </div>
    </div>
  </div>
</template>

<script>
import HeroSection from '@/components/index/HeroSection.vue'
import CategoriesSection from '@/components/index/CategoriesSection.vue'
import FeaturedSection from '@/components/index/FeaturedSection.vue'
import MastersSection from '@/components/index/MastersSection.vue'

export default {
  name: 'HomeView',
  components: {
    HeroSection,
    CategoriesSection,
    FeaturedSection,
    MastersSection
  },
  data() {
    return {
      currentSection: 0,
      totalSections: 4,
      isScrolling: false,
      touchStartY: 0,
      touchEndY: 0,
      sections: [
        { title: '首页' },
        { title: '分类' },
        { title: '精选' },
        { title: '大师' }
      ]
    }
  },
  mounted() {
    // 监听键盘事件
    window.addEventListener('keydown', this.handleKeydown)

    // 防止默认滚动行为
    document.body.style.overflow = 'hidden'
  },
  beforeUnmount() {
    window.removeEventListener('keydown', this.handleKeydown)
    document.body.style.overflow = 'auto'
  },
  methods: {
    // 鼠标滚轮事件
    handleWheel(event) {
      if (this.isScrolling) return

      event.preventDefault()

      if (event.deltaY > 0) {
        // 向下滚动
        this.nextSection()
      } else {
        // 向上滚动
        this.prevSection()
      }
    },

    // 触摸开始
    handleTouchStart(event) {
      this.touchStartY = event.touches[0].clientY
    },

    // 触摸移动
    handleTouchMove(event) {
      if (this.isScrolling) return

      event.preventDefault()
      this.touchEndY = event.touches[0].clientY

      const deltaY = this.touchStartY - this.touchEndY
      const threshold = 50 // 滑动阈值

      if (Math.abs(deltaY) > threshold) {
        if (deltaY > 0) {
          // 向上滑动（下一屏）
          this.nextSection()
        } else {
          // 向下滑动（上一屏）
          this.prevSection()
        }
      }
    },

    // 键盘事件
    handleKeydown(event) {
      if (this.isScrolling) return

      switch (event.key) {
        case 'ArrowDown':
        case 'PageDown':
          event.preventDefault()
          this.nextSection()
          break
        case 'ArrowUp':
        case 'PageUp':
          event.preventDefault()
          this.prevSection()
          break
        case 'Home':
          event.preventDefault()
          this.goToSection(0)
          break
        case 'End':
          event.preventDefault()
          this.goToSection(this.totalSections - 1)
          break
      }
    },

    // 下一屏
    nextSection() {
      if (this.currentSection < this.totalSections - 1) {
        this.goToSection(this.currentSection + 1)
      }
    },

    // 上一屏
    prevSection() {
      if (this.currentSection > 0) {
        this.goToSection(this.currentSection - 1)
      }
    },

    // 跳转到指定屏
    goToSection(index) {
      if (index >= 0 && index < this.totalSections && index !== this.currentSection) {
        this.isScrolling = true
        this.currentSection = index

        // 滚动动画完成后重置状态
        setTimeout(() => {
          this.isScrolling = false
        }, 800)
      }
    }
  }
}
</script>

<style scoped>
.home-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.fullpage-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.sections-wrapper {
  width: 100%;
  height: 100%;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fullpage-section {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 英雄区域全屏样式 */
.hero-section {
  display: block !important;
  align-items: unset !important;
  justify-content: unset !important;
}

.fullpage-section:nth-child(2) {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.fullpage-section:nth-child(3) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.fullpage-section:nth-child(4) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.section-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

/* 右侧导航指示器 */
.section-nav {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.nav-dot {
  position: relative;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.nav-dot.active {
  background: white;
  border-color: white;
  transform: scale(1.3);
}

.nav-label {
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.nav-dot:hover .nav-label {
  opacity: 1;
}

/* 滚动提示 */
.scroll-hint {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  z-index: 10;
  animation: bounce 2s infinite;
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }

  40% {
    transform: translateX(-50%) translateY(-10px);
  }

  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

.scroll-arrow {
  margin-bottom: 8px;
  opacity: 0.8;
}

.scroll-text {
  font-size: 14px;
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-nav {
    right: 20px;
    gap: 15px;
  }

  .nav-dot {
    width: 10px;
    height: 10px;
  }

  .scroll-hint {
    bottom: 20px;
  }

  .scroll-text {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .section-content {
    padding: 0 15px;
  }

  .section-nav {
    right: 15px;
  }
}
</style>